{"name": "@serialport/binding-mock", "version": "10.2.2", "description": "The mock serialport bindings", "types": "./dist/index.d.ts", "main": "./dist/index.js", "exports": {"require": "./dist/index.js", "default": "./dist/index-esm.mjs"}, "engines": {"node": ">=12.0.0"}, "repository": "**************:serialport/binding-mock.git", "homepage": "https://github.com/serialport/binding-mock", "scripts": {"test": "mocha", "lint": "tsc && eslint lib/**/*.ts", "format": "eslint lib/**/*.ts --fix", "clean": "rm -rf dist-ts dist", "build": "npm run clean && tsc -p tsconfig-build.json && rollup -c && node -r esbuild-register bundle-types", "prepublishOnly": "npm run build", "semantic-release": "semantic-release"}, "keywords": ["serialport-binding", "debug"], "license": "MIT", "devDependencies": {"@microsoft/api-extractor": "7.19.4", "@types/chai": "4.3.0", "@types/mocha": "9.1.0", "@types/node": "17.0.15", "@typescript-eslint/eslint-plugin": "5.10.2", "@typescript-eslint/parser": "5.10.2", "chai": "4.3.6", "esbuild": "0.14.18", "esbuild-register": "3.3.2", "eslint": "8.8.0", "mocha": "9.2.0", "rollup": "2.67.0", "rollup-plugin-node-resolve": "5.2.0", "semantic-release": "19.0.2", "typescript": "4.5.5"}, "mocha": {"bail": true, "require": ["esbuild-register"], "spec": "lib/**/*-test.ts"}, "dependencies": {"@serialport/bindings-interface": "^1.2.1", "debug": "^4.3.3"}}