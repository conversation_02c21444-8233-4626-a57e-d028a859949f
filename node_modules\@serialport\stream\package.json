{"name": "@serialport/stream", "version": "11.0.1", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc --build tsconfig-build.json"}, "dependencies": {"@serialport/bindings-interface": "1.2.2", "debug": "4.3.4"}, "devDependencies": {"@serialport/binding-mock": "^10.2.2", "typescript": "5.1.6"}, "engines": {"node": ">=12.0.0"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/serialport/node-serialport.git"}, "funding": "https://opencollective.com/serialport/donate", "gitHead": "12aeb531260f53d0adce42304db5074db85cc8d9"}