// 引入所需模块
const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
const path = require('path');
const ModbusRTU = require('modbus-serial');
const client = new ModbusRTU();
const fs = require('fs');

// CSV数据记录配置
const csvFilePath = path.join(__dirname, 'plc_data_history.csv');
const csvHeader = '时间,数字量输入01-24,数字量输出01-24,模拟量01-16,温度01-16,连接状态\n';

// 初始化CSV文件（如果不存在则创建）
function initCSVFile() {
    if (!fs.existsSync(csvFilePath)) {
        fs.writeFileSync(csvFilePath, csvHeader, 'utf8');
        console.log(`📄 CSV数据记录文件已创建: ${csvFilePath}`);
    } else {
        console.log(`📄 CSV数据记录文件已存在: ${csvFilePath}`);
    }
}

// 记录数据到CSV文件
function saveDataToCSV(userData, connectionStatus) {
    try {
        const timestamp = new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        // 将布尔数组转换为0/1字符串
        const digitalInputs = userData.page1Data.map(val => val ? '1' : '0').join('');
        const digitalOutputs = userData.page2Data.map(val => val ? '1' : '0').join('');

        // 模拟量和温度数据用逗号分隔
        const analogInputs = userData.page3Data.join(',');
        const temperatures = userData.page4Data.join(',');

        const csvLine = `"${timestamp}","${digitalInputs}","${digitalOutputs}","${analogInputs}","${temperatures}","${connectionStatus}"\n`;

        fs.appendFileSync(csvFilePath, csvLine, 'utf8');
        console.log(`💾 数据已记录到CSV: ${timestamp}`);
    } catch (error) {
        console.error('❌ CSV记录失败:', error.message);
    }
}

// 创建 Express 应用和 HTTP 服务器
const app = express();
const server = http.createServer(app);
const io = new Server(server, { cors: { origin: '*' } }); // 启用跨域

// 读取配置文件
const config = JSON.parse(fs.readFileSync(path.join(__dirname, 'config.json'), 'utf-8'));
const plcIP = config.plcIP;
const plcPort = config.plcPort;
const plcSlaveId = config.plcSlaveId;
const pollInterval = config.pollInterval;
const startAddress = config.startAddress || 0;
const registerCount = config.registerCount || 35;

let isConnected = false; // 连接状态标志
let isReconnecting = false; // 重连状态标志，防止重复重连

// TCP连接函数，支持断线重连
function connectTCP() {
    if (isReconnecting) {
        console.log('🔄 重连已在进行中，跳过重复请求');
        return;
    }
    isReconnecting = true;

    client.connectTCP(plcIP, { port: plcPort })
        .then(async () => {
            console.log(`TCP连接已建立 (IP: ${plcIP}, 端口: ${plcPort})，正在验证Modbus通信...`);

            // 尝试读取一个寄存器来验证Modbus通信
            try {
                await client.setID(plcSlaveId);
                await client.readHoldingRegisters(startAddress, 1); // 只读取1个寄存器进行测试

                // 如果读取成功，才认为PLC真正连接
                isConnected = true;
                isReconnecting = false; // 重置重连状态
                console.log(`✅ PLC连接成功并验证 (IP: ${plcIP}, 端口: ${plcPort})`);
                io.emit('plcStatus', {
                    connected: true,
                    ip: plcIP,
                    port: plcPort
                });
            } catch (modbusErr) {
                // Modbus通信失败
                isConnected = false;
                console.error(`❌ TCP连接成功但Modbus通信失败: ${modbusErr.message}`);
                io.emit('plcStatus', {
                    connected: false,
                    error: `Modbus通信失败: ${modbusErr.message}`
                });
                client.close(); // 关闭无效连接
                setTimeout(() => {
                    isReconnecting = false; // 重置状态后再重连
                    connectTCP();
                }, 4000);
            }
        })
        .catch(err => {
            isConnected = false;
            console.error('❌ TCP连接失败:', err.message);
            io.emit('plcStatus', {
                connected: false,
                error: `TCP连接失败: ${err.message}`
            });
            setTimeout(() => {
                isReconnecting = false; // 重置状态后再重连
                connectTCP();
            }, 4000);
        });
}
// 初始化CSV文件
initCSVFile();

connectTCP(); // 启动时自动连接

// 监听TCP关闭和错误，自动重连
client.on('close', () => {
    isConnected = false;
    console.log('与PLC的TCP连接已关闭，尝试重连...');
    io.emit('plcStatus', {
        connected: false,
        error: 'PLC未连接'
    });
    if (!isReconnecting) {
        setTimeout(() => {
            isReconnecting = false; // 重置状态后再重连
            connectTCP();
        }, 4000);
    }
});
client.on('error', err => {
    isConnected = false;
    console.error('PLC通信错误:', err.message);
    io.emit('plcStatus', {
        connected: false,
        error: 'PLC未连接'
    });
    if (!isReconnecting) {
        setTimeout(() => {
            isReconnecting = false; // 重置状态后再重连
            connectTCP();
        }, 4000);
    }
});

// 新增：解析数字量数据函数
// 将6个字节（48位）解析为两个包含24个布尔值（true/false）的数组
function parseDigitalData(bytes) {
    const inputs = [];
    const outputs = [];

    // 安全检查
    if (!bytes || bytes.length < 6) {
        console.warn(`⚠️ 数字量数据长度不足: 期望6字节，实际${bytes ? bytes.length : 0}字节`);
        // 返回默认的24个false值
        return {
            inputs: new Array(24).fill(false),
            outputs: new Array(24).fill(false)
        };
    }

    // 处理前3个字节（输入）
    for (let i = 0; i < 3; i++) {
        for (let j = 0; j < 8; j++) {
            // (bytes[i] >> j) & 1 会得到第j位的值 (0或1)
            inputs.push(!!((bytes[i] >> j) & 1));
        }
    }
    // 处理后3个字节（输出）
    for (let i = 3; i < 6; i++) {
        for (let j = 0; j < 8; j++) {
            outputs.push(!!((bytes[i] >> j) & 1));
        }
    }
    return { inputs, outputs };
}

// 静态文件服务，前端页面资源
app.use(express.static(path.join(__dirname, 'public')));
// 默认主页
app.get('/', (_, res) => {
    res.sendFile(path.join(__dirname, 'public', '0.html'));
});
// 技术文档路由
app.get('/docs', (_, res) => {
    res.sendFile(path.join(__dirname, '技术文档.html'));
});

// 数据分发函数：将70字节原始数据分配给4个页面
function distributeDataToPages(data) {
    // 数据长度检查
    if (!data || data.length < 70) {
        console.warn(`⚠️ 数据长度不足: 期望70字节，实际${data ? data.length : 0}字节`);
        // 返回默认数据
        return {
            page1Data: new Array(24).fill(false),
            page2Data: new Array(24).fill(false),
            page3Data: new Array(16).fill('0.00'),
            page4Data: new Array(16).fill('0.0')
        };
    }

    // 解析前6个字节的数字量
    const digitalData = parseDigitalData(data.slice(0, 6));

    // 解析模拟量输入 (字节6到37)
    const analogInputs = [];
    for (let i = 6; i < 38 && i + 1 < data.length; i += 2) {
        const highByte = data[i] || 0;
        const lowByte = data[i+1] || 0;
        const value = (highByte << 8) | lowByte;
        analogInputs.push((value / 100).toFixed(2));
    }
    // 确保有16个元素
    while (analogInputs.length < 16) {
        analogInputs.push('0.00');
    }

    // 解析温度输入 (字节38到69)
    const temperatures = [];
    for (let i = 38; i < 70 && i + 1 < data.length; i += 2) {
        const highByte = data[i] || 0;
        const lowByte = data[i+1] || 0;
        const value = (highByte << 8) | lowByte;
        temperatures.push((value / 10).toFixed(1));
    }
    // 确保有16个元素
    while (temperatures.length < 16) {
        temperatures.push('0.0');
    }

    return {
        page1Data: digitalData.inputs,     // 包含24个布尔值的数组
        page2Data: digitalData.outputs,    // 包含24个布尔值的数组
        page3Data: analogInputs,           // 包含16个模拟量字符串的数组
        page4Data: temperatures            // 包含16个温度字符串的数组
    };
}

// 定时读取Modbus数据并推送到前端
setInterval(async () => {
    if (!isConnected) return; // 未连接时跳过
    try {
        await client.setID(plcSlaveId); // 设置Modbus从站ID
        const data = await client.readHoldingRegisters(startAddress, registerCount); // 使用配置参数

        // 处理Modbus数据格式：每个寄存器是16位(2字节)
        let rawBytes;
        if (data.buffer) {
            // 如果有buffer属性，直接使用
            rawBytes = Array.from(new Uint8Array(data.buffer));
        } else if (data.data) {
            // 如果data是寄存器数组，需要转换为字节数组
            rawBytes = [];
            for (let i = 0; i < data.data.length; i++) {
                const register = data.data[i];
                rawBytes.push((register >> 8) & 0xFF); // 高字节
                rawBytes.push(register & 0xFF);        // 低字节
            }
        } else {
            // 直接使用data作为字节数组
            rawBytes = Array.isArray(data) ? data : [data];
        }

        console.log(`📊 读取到${registerCount}个寄存器，转换为${rawBytes.length}字节数据`);
        const userData = distributeDataToPages(rawBytes); // 分发数据
        io.emit('dataUpdate', userData); // 推送到所有前端

        // 记录数据到CSV文件
        saveDataToCSV(userData, 'PLC已连接');
    } catch (err) {
        console.error('❌ Modbus读取失败:', err.message);

        // 如果读取失败，说明连接已断开，更新状态
        isConnected = false;
        io.emit('plcStatus', {
            connected: false,
            error: `Modbus读取失败: ${err.message}`
        });
        io.emit('dataUpdate', null); // 读取失败时推送null

        // 尝试重新连接
        console.log('🔄 检测到连接断开，尝试重新连接...');
        if (!isReconnecting) {
            setTimeout(() => {
                isReconnecting = false; // 重置状态后再重连
                connectTCP();
            }, 2000);
        }
    }
}, pollInterval);

// Socket.IO连接处理
io.on('connection', (socket) => {
    console.log('新的WebSocket客户端连接');
    // 立即发送当前PLC状态
    socket.emit('plcStatus', { 
        connected: isConnected,
        ip: plcIP,
        port: plcPort
    });
});

// 启动HTTP服务器
const PORT = process.env.PORT || 3005;
server.listen(PORT, '0.0.0.0', () => {
    console.log(`Server running on http://localhost:${PORT}`);
    console.log(`局域网访问地址: http://[您的IP]:${PORT}`);
    console.log('Modbus RTU数据采集系统已启动');
    console.log('当前模式: 真实数据模式');
    console.log(`轮询频率: ${(1000 / pollInterval).toFixed(2)} Hz`);
});
