{"name": "@serialport/parser-packet-length", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc --build tsconfig-build.json"}, "version": "11.0.1", "engines": {"node": ">=8.6.0"}, "publishConfig": {"access": "public"}, "license": "MIT", "repository": {"type": "git", "url": "git://github.com/serialport/node-serialport.git"}, "devDependencies": {"typescript": "5.1.6"}, "gitHead": "12aeb531260f53d0adce42304db5074db85cc8d9"}