{"name": "crc", "version": "3.4.0", "description": "Module for calculating Cyclic Redundancy Check (CRC) for Node.js and the Browser.", "keywords": ["crc"], "files": ["lib"], "main": "./lib/index.js", "scripts": {"test": "mocha test/*.test.js", "pretest": "cd src && babel --out-dir ../lib *.js"}, "author": {"name": "<PERSON>", "url": "https://github.com/alex<PERSON><PERSON><PERSON>"}, "devDependencies": {"babel-core": "^6.1.21", "babel-preset-es2015": "^6.1.18", "beautify-benchmark": "^0.2.4", "benchmark": "^1.0.0", "buffer-crc32": "^0.2.3", "chai": "^3.4.1", "mocha": "*", "seedrandom": "^2.3.6"}, "homepage": "https://github.com/alex<PERSON><PERSON><PERSON>/node-crc", "bugs": "https://github.com/alex<PERSON><PERSON><PERSON>/node-crc/issues", "repository": {"type": "git", "url": "git://github.com/alexgorbatchev/node-crc.git"}, "license": "MIT", "babel": {"presets": ["es2015"]}}