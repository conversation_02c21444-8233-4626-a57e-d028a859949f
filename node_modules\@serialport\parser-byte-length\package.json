{"name": "@serialport/parser-byte-length", "version": "11.0.1", "main": "./dist/index.js", "types": "./dist/index.d.ts", "engines": {"node": ">=12.0.0"}, "publishConfig": {"access": "public"}, "license": "MIT", "scripts": {"build": "tsc --build tsconfig-build.json"}, "repository": {"type": "git", "url": "git://github.com/serialport/node-serialport.git"}, "funding": "https://opencollective.com/serialport/donate", "devDependencies": {"typescript": "5.1.6"}, "gitHead": "12aeb531260f53d0adce42304db5074db85cc8d9"}